#!/usr/bin/env python3
"""
Test the standard chat interface specifically
"""

import sys
import os
sys.path.append('.')

from ai_cli.config import Config
from ai_cli.chat import ChatInterface
from rich.console import Console

console = Console()

def test_standard_interface():
    """Test the standard chat interface offline responses"""
    console.print('[bold blue]🧪 Testing Standard Chat Interface[/bold blue]')
    console.print("=" * 50)
    
    config = Config()
    config.demo_mode = True  # Force demo mode
    
    chat = ChatInterface(config)
    
    # Test questions
    test_questions = [
        'write a c program for check oddno',
        'write a java program to list 1 to 100', 
        'any program',
        'hi',
        'what is your name'
    ]
    
    for i, question in enumerate(test_questions, 1):
        console.print(f'\n[yellow]Test {i}: "{question}"[/yellow]')
        
        # Test the _handle_offline_response method
        try:
            # Simulate the offline response handling
            user_lower = question.lower()
            
            # Check if it matches our improved patterns
            if any(keyword in user_lower for keyword in ['c program', 'c progarm', 'c code']) and any(keyword in user_lower for keyword in ['odd', 'even']):
                console.print("[green]✅ GOOD - Matches C program pattern[/green]")
                console.print("[cyan]Would return: C program for odd/even checking[/cyan]")
            elif 'java' in user_lower and any(keyword in user_lower for keyword in ['list', 'print', '1 to 100', '1-100', 'numbers']):
                console.print("[green]✅ GOOD - Matches Java list pattern[/green]")
                console.print("[cyan]Would return: Java program to list 1-100[/cyan]")
            elif any(keyword in user_lower for keyword in ['any program', 'other program', 'another program']):
                console.print("[green]✅ GOOD - Matches 'any program' pattern[/green]")
                console.print("[cyan]Would return: Useful example program[/cyan]")
            elif any(greeting in user_lower for greeting in ['hi', 'hello', 'hey']):
                console.print("[green]✅ GOOD - Matches greeting pattern[/green]")
                console.print("[cyan]Would return: Friendly greeting[/cyan]")
            elif any(phrase in user_lower for phrase in ['what is your name', 'what is you name', 'who are you']):
                console.print("[green]✅ GOOD - Matches name question pattern[/green]")
                console.print("[cyan]Would return: AI identification[/cyan]")
            else:
                console.print("[yellow]⚠️ Would use fallback response[/yellow]")
                
        except Exception as e:
            console.print(f"[red]❌ ERROR: {str(e)}[/red]")

def test_actual_offline_method():
    """Test the actual _handle_offline_response method"""
    console.print(f'\n[bold blue]🔧 Testing Actual Offline Response Method[/bold blue]')
    console.print("=" * 50)
    
    config = Config()
    config.demo_mode = True
    
    chat = ChatInterface(config)
    
    # Test a few key questions by calling the actual method
    test_cases = [
        "write a c program for check oddno",
        "any program", 
        "hi"
    ]
    
    for question in test_cases:
        console.print(f'\n[yellow]Testing: "{question}"[/yellow]')
        
        try:
            # The method prints directly, so we can't capture the return
            # But we can verify it doesn't crash
            chat._handle_offline_response(question, 1.5)
            console.print("[green]✅ Method executed successfully[/green]")
        except Exception as e:
            console.print(f"[red]❌ Method failed: {str(e)}[/red]")

if __name__ == "__main__":
    test_standard_interface()
    test_actual_offline_method()
