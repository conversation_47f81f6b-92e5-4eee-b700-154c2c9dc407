{"time":"2025-07-30T18:21:46.9562957+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-07-30T18:21:46.9585646+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-07-30T18:21:47.5684139+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-07-30T18:21:47.6642285+05:30","level":"INFO","msg":"OK   20250424200609_initial.sql (3.93ms)"}
{"time":"2025-07-30T18:21:47.6674403+05:30","level":"INFO","msg":"OK   20250515105448_add_summary_message_id.sql (3.21ms)"}
{"time":"2025-07-30T18:21:47.6679412+05:30","level":"INFO","msg":"OK   20250624000000_add_created_at_indexes.sql (500.9µs)"}
{"time":"2025-07-30T18:21:47.6682536+05:30","level":"INFO","msg":"OK   **************_add_provider_to_messages.sql (312.4µs)"}
{"time":"2025-07-30T18:21:47.6682536+05:30","level":"INFO","msg":"goose: successfully migrated database to version: **************"}
{"time":"2025-07-30T18:21:47.6682536+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-07-30T18:21:47.6868564+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-07-30T18:21:47.6868564+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-07-30T18:21:47.6969141+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-07-30T18:21:47.6969141+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-07-30T18:21:49.9478883+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-07-30T18:21:49.9478883+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-07-30T18:21:49.9560501+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-07-30T18:21:49.9579243+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-07-30T18:21:53.5031621+05:30","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processGeneration.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":369},"msg":"failed to generate title","error":"POST \"https://openrouter.ai/api/v1/chat/completions\": 402 Payment Required {\"message\":\"This request requires more credits, or fewer max_tokens. You requested up to 4096 tokens, but can only afford 2904. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account\",\"code\":402,\"metadata\":{\"provider_name\":null}}"}
{"time":"2025-07-30T18:23:33.379327+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-07-30T18:23:33.3806305+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-07-30T18:23:34.0513254+05:30","level":"INFO","msg":"goose: no migrations to run. current version: **************"}
{"time":"2025-07-30T18:23:34.0513254+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-07-30T18:23:34.0673736+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-07-30T18:23:34.0673736+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-07-30T18:23:34.0767217+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":171},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-07-30T18:23:34.0767217+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":173},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-07-30T18:23:34.6388958+05:30","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-07-30T18:24:12.6108593+05:30","level":"ERROR","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.(*agent).processGeneration.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":369},"msg":"failed to generate title","error":"POST \"https://openrouter.ai/api/v1/chat/completions\": 402 Payment Required {\"message\":\"This request requires more credits, or fewer max_tokens. You requested up to 4096 tokens, but can only afford 2904. To increase, visit https://openrouter.ai/settings/credits and upgrade to a paid account\",\"code\":402,\"metadata\":{\"provider_name\":null}}"}
