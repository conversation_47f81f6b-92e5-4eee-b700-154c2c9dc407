#!/usr/bin/env python3
"""
Test the full chat flow including user input analysis and AI response timing
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ai_cli.config import Config
from ai_cli.chat import ChatInterface
from ai_cli.utils import get_real_time_token_count

def test_full_chat_flow():
    """Test the full chat flow with enhanced features"""
    print("🧪 Testing Full Chat Flow with Enhanced Features")
    print("=" * 50)
    
    try:
        # Create configuration
        config = Config()
        print(f"✓ Configuration created (API key: {bool(config.api_key)})")
        
        # Create chat interface
        chat_interface = ChatInterface(config)
        print("✓ Chat interface created")
        print(f"✓ Token counter active: {chat_interface.token_counter_active}")
        
        # Test user input analysis directly
        print("\n💬 Testing user input analysis...")
        
        test_messages = [
            "Hello! How are you today?",
            "Can you write a Python function to sort a list?",
            "Short message.",
            "This is a comprehensive test message with multiple words to verify that the enhanced token counting, word breakdown, and character analysis features are working correctly in the chat interface."
        ]
        
        for i, test_message in enumerate(test_messages, 1):
            print(f"\n--- User Input Test {i} ---")
            print(f"Simulating user input: '{test_message}'")
            
            # Simulate the user input processing that happens in _get_user_input_enhanced
            user_input = test_message
            if user_input.strip():
                token_info = get_real_time_token_count(user_input, config.model)
                print(f"📊 Your message: {token_info['formatted']} | {token_info['words']} words | {token_info['characters']} characters")
                
                # Show detailed word breakdown
                if token_info['word_breakdown']['breakdown_text']:
                    print(f"📝 Word breakdown: {token_info['word_breakdown']['breakdown_text']}")
            
            # Test offline response with timing
            print("Testing AI response with timing...")
            chat_interface.demo_mode = True
            chat_interface._process_user_message(test_message)
        
        print("\n✅ Full chat flow test completed!")
        
    except Exception as e:
        print(f"❌ Full chat flow test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_full_chat_flow()
