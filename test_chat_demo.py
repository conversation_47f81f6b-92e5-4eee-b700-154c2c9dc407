#!/usr/bin/env python3
"""
Test the chat interface in demo mode with a simple interaction
"""

import sys
import os
import time

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from rich.console import Console
from ai_cli.config import Config
from ai_cli.chat import ChatInterface

def simulate_chat_interaction():
    """Simulate a chat interaction to test the improved responses"""
    console = Console()
    console.print("[bold cyan]🧪 Simulating Chat Interaction[/bold cyan]")
    console.print()
    
    # Initialize chat in demo mode
    config = Config()
    config.demo_mode = True
    chat = ChatInterface(config)
    
    # Test the problematic questions
    test_questions = [
        "make a java program to check prime no",
        "what is your name", 
        "you are which ai",
        "my name is rahul"
    ]
    
    console.print("[bold]Simulating the exact questions from your example:[/bold]")
    console.print()
    
    for question in test_questions:
        console.print(f"[green]You:[/green] {question}")
        
        # Simulate the thinking animation briefly
        console.print("[dim]⠴ AI is thinking[/dim]")
        time.sleep(0.5)
        
        # Process the message
        chat._process_user_message(question)
        console.print()
    
    console.print("[bold green]✅ Chat simulation complete![/bold green]")
    console.print("\n[yellow]The AI should now provide much better responses to your questions![/yellow]")
    console.print("\n[cyan]Try running the actual chat interface:[/cyan]")
    console.print("• python -m ai_cli.main --demo")
    console.print("• python -m ai_cli.main --enhanced --demo")

if __name__ == "__main__":
    simulate_chat_interaction()
