#!/usr/bin/env python3
"""
Direct test of the chat interface
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ai_cli.config import Config
from ai_cli.chat import ChatInterface

def test_chat_interface():
    """Test the chat interface directly"""
    print("🧪 Testing Chat Interface Directly")
    print("=" * 50)
    
    try:
        # Create configuration
        config = Config()
        print(f"✓ Configuration created (API key: {bool(config.api_key)})")
        
        # Create chat interface
        chat_interface = ChatInterface(config)
        print("✓ Chat interface created")
        
        # Test connection
        print("\n🔗 Testing API connection...")
        success, message = chat_interface.client.test_connection()
        
        if success:
            print("✅ API connection successful!")
            
            # Test a simple message processing
            print("\n💬 Testing message processing...")
            
            # Test enhanced token counting and timing with different message lengths
            test_messages = [
                "Hello! Can you tell me what 2+2 equals?",
                "This is a longer message to test the enhanced token counting accuracy and timing functionality.",
                "Short test.",
                "Write a Python function to calculate fibonacci numbers"
            ]

            for i, test_message in enumerate(test_messages, 1):
                print(f"\n--- Test {i} ---")
                print(f"User: {test_message}")

                # Process the message
                chat_interface._process_user_message(test_message)
            
            print("\n✅ Message processing test completed!")
            
        else:
            print(f"⚠️  API connection failed: {message}")
            print("Testing offline mode...")

            # Test offline mode
            chat_interface.offline_mode = True
            test_message = "Hello! This is a test in offline mode."
            print(f"User: {test_message}")

            chat_interface._process_user_message(test_message)

            print("\n✅ Offline mode test completed!")

        # Also test demo mode explicitly
        print("\n🎭 Testing demo mode...")
        chat_interface.demo_mode = True
        chat_interface.offline_mode = False
        test_message = "Test demo mode with code request: write a Python function"
        print(f"User: {test_message}")
        chat_interface._process_user_message(test_message)
        print("\n✅ Demo mode test completed!")
        
        print("\n🎉 All chat interface tests passed!")
        
    except Exception as e:
        print(f"❌ Chat interface test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_chat_interface()
