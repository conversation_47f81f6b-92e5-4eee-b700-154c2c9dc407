#!/usr/bin/env python3
"""
Test the improved demo responses
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from rich.console import Console
from ai_cli.config import Config
from ai_cli.chat import Chat<PERSON>nterface
from ai_cli.enhanced_chat import EnhancedChatInterface

def test_improved_responses():
    """Test the improved demo responses"""
    console = Console()
    console.print("[bold cyan]🧪 Testing Improved Demo Responses[/bold cyan]")
    console.print()
    
    # Test questions that were problematic before
    test_cases = [
        "make a java program to check prime no",
        "what is your name", 
        "you are which ai",
        "my name is rahul"
    ]
    
    # Test basic chat interface
    console.print("[bold]1. Testing Basic Chat Interface[/bold]")
    config = Config()
    config.demo_mode = True
    chat = ChatInterface(config)
    
    for i, test_input in enumerate(test_cases, 1):
        console.print(f"\n[yellow]Test {i}: '{test_input}'[/yellow]")
        
        # Simulate the offline response handling
        chat._handle_offline_response(test_input, 2.0)
        console.print()
    
    # Test enhanced chat interface  
    console.print("\n[bold]2. Testing Enhanced Chat Interface[/bold]")
    enhanced_chat = EnhancedChatInterface(config)
    
    for i, test_input in enumerate(test_cases, 1):
        console.print(f"\n[yellow]Enhanced Test {i}: '{test_input}'[/yellow]")
        
        # Get demo responses
        responses = enhanced_chat._get_demo_responses(test_input)
        response = responses[0] if responses else "No response"
        
        enhanced_chat._display_claude_ai_response(response, 2.5)
        console.print()
    
    console.print("[bold green]🎉 Improved Demo Response Test Complete![/bold green]")
    console.print("\n[cyan]The AI should now provide much better, more specific responses![/cyan]")

if __name__ == "__main__":
    test_improved_responses()
