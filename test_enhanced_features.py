#!/usr/bin/env python3
"""
Test script to demonstrate enhanced chat features
"""

import sys
import os
import time

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ai_cli.config import Config
from ai_cli.enhanced_chat import EnhancedChatInterface
from ai_cli.utils import get_real_time_token_count, get_session_cost_summary
from ai_cli.api_client import Message
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

def test_token_counter():
    """Test the real-time token counter"""
    console = Console()
    console.print("[bold cyan]🧪 Testing Token Counter[/bold cyan]")
    
    test_texts = [
        "Hello, world!",
        "Write a Python function to calculate fibonacci numbers",
        "```python\ndef fibonacci(n):\n    if n <= 1:\n        return n\n    return fibonacci(n-1) + fibonacci(n-2)\n```"
    ]
    
    for text in test_texts:
        token_info = get_real_time_token_count(text, "qwen/qwen-2.5-coder-32b-instruct:free", demo_mode=True)
        console.print(f"Text: {text[:50]}...")
        console.print(f"   📊 {token_info['formatted']} | {token_info['words']} words | {token_info['characters']} chars")
        console.print()

def test_demo_mode():
    """Test demo mode functionality"""
    console = Console()
    console.print("[bold yellow]🎭 Testing Demo Mode[/bold yellow]")
    
    # Test with demo config
    config = Config()
    config.demo_mode = True
    
    console.print(f"Demo mode active: {config.is_demo_mode}")
    console.print(f"API key: {'[HIDDEN]' if config.api_key else 'None'}")
    console.print()

def test_cost_summary():
    """Test session cost summary"""
    console = Console()
    console.print("[bold green]💰 Testing Cost Summary[/bold green]")
    
    # Create mock conversation history
    mock_history = [
        Message(role="user", content="Hello, can you help me with Python?"),
        Message(role="assistant", content="Of course! I'd be happy to help you with Python programming. What specific topic would you like to learn about?"),
        Message(role="user", content="Write a function to sort a list"),
        Message(role="assistant", content="Here's a simple sorting function:\n\n```python\ndef sort_list(lst):\n    return sorted(lst)\n\n# Example usage\nnumbers = [3, 1, 4, 1, 5, 9, 2, 6]\nsorted_numbers = sort_list(numbers)\nprint(sorted_numbers)\n```")
    ]
    
    cost_summary = get_session_cost_summary(mock_history, "qwen/qwen-2.5-coder-32b-instruct:free", demo_mode=True)
    
    console.print(f"Total tokens: {cost_summary['total_tokens']}")
    console.print(f"User tokens: {cost_summary['user_tokens']}")
    console.print(f"AI tokens: {cost_summary['ai_tokens']}")
    console.print(f"Total cost: {cost_summary['formatted_cost']}")
    console.print()

def test_animations():
    """Test animation system"""
    console = Console()
    console.print("[bold blue]🎬 Testing Animation System[/bold blue]")
    
    # Create enhanced chat interface
    config = Config()
    config.demo_mode = True
    chat = EnhancedChatInterface(config)
    
    # Test animation components
    console.print("Testing thinking animation:")
    thinking = chat._create_claude_thinking_animation()
    console.print(f"   {thinking}")
    
    console.print("Testing writing animation:")
    writing = chat._create_claude_writing_animation()
    console.print(f"   {writing}")
    
    console.print("Testing testing animation:")
    testing = chat._create_claude_testing_animation()
    console.print(f"   {testing}")
    console.print()

def test_ui_components():
    """Test UI components"""
    console = Console()
    console.print("[bold magenta]🎨 Testing UI Components[/bold magenta]")
    
    # Test welcome display
    config = Config()
    config.demo_mode = True
    chat = EnhancedChatInterface(config)
    
    console.print("Welcome display test:")
    console.print("✅ Enhanced welcome interface ready")
    
    console.print("Input styling test:")
    console.print("✅ Claude-like input prompts ready")
    
    console.print("Response formatting test:")
    console.print("✅ Enhanced response panels ready")
    console.print()

def main():
    """Run all tests"""
    console = Console()
    
    # Main header
    header = Panel(
        Text.assemble(
            ("🚀 Enhanced Chat Interface Test Suite\n", "bold cyan"),
            ("Testing all new features and enhancements", "cyan")
        ),
        title="Feature Tests",
        border_style="cyan",
        padding=(1, 2)
    )
    console.print(header)
    console.print()
    
    # Run tests
    test_token_counter()
    test_demo_mode()
    test_cost_summary()
    test_animations()
    test_ui_components()
    
    # Summary
    summary = Panel(
        Text.assemble(
            ("✅ All enhanced features tested successfully!\n\n", "bold green"),
            ("Features implemented:\n", "bold"),
            ("• Real-time token counter with cost estimation\n", "green"),
            ("• 3-stage AI response animation system\n", "green"),
            ("• Claude-like UI design and styling\n", "green"),
            ("• Demo mode with mock pricing\n", "green"),
            ("• Enhanced keyboard shortcuts\n", "green"),
            ("• Session cost summary\n", "green"),
            ("• Improved error handling and fallbacks\n", "green"),
            ("\nRun 'python enhanced_chat_demo.py' to try the full interface!", "cyan bold")
        ),
        title="🎉 Test Results",
        border_style="green",
        padding=(1, 2)
    )
    console.print(summary)

if __name__ == "__main__":
    main()
