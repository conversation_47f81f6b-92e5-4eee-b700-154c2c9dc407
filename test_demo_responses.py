#!/usr/bin/env python3
"""
Test the improved demo responses
"""

import sys
import os
sys.path.append('.')

from ai_cli.config import Config
from ai_cli.enhanced_chat import EnhancedChatInterface
from rich.console import Console

console = Console()

def test_demo_responses():
    console.print('[bold blue]🧪 Testing Improved Demo Responses[/bold blue]')
    console.print("=" * 50)

    config = Config()
    config.demo_mode = True  # Force demo mode

    chat = EnhancedChatInterface(config)

    # Test the exact questions that were failing
    test_questions = [
        'write a c program for check oddno',
        'write a java program to list 1 to 100', 
        'any program',
        'hi',
        'what is your name'
    ]

    for i, question in enumerate(test_questions, 1):
        console.print(f'\n[yellow]Test {i}: "{question}"[/yellow]')
        responses = chat._get_demo_responses(question)
        response = responses[0] if responses else 'No response'
        
        # Check if response is appropriate
        is_good = False
        if 'c program' in question.lower() and 'odd' in question.lower():
            is_good = '#include' in response and 'odd' in response.lower()
        elif 'java' in question.lower() and '1 to 100' in question.lower():
            is_good = 'class' in response and 'for' in response
        elif 'any program' in question.lower():
            is_good = len(response) > 100 and ('def ' in response or 'class' in response)
        elif question.lower() in ['hi', 'hello']:
            is_good = 'hello' in response.lower() or 'hi' in response.lower()
        elif 'name' in question.lower():
            is_good = 'qwen' in response.lower() or 'ai' in response.lower()
        
        status = "[green]✅ GOOD[/green]" if is_good else "[red]❌ NEEDS FIX[/red]"
        console.print(f'Status: {status}')
        
        # Show first 200 characters of response
        preview = response[:200] + '...' if len(response) > 200 else response
        console.print(f'[cyan]Response:[/cyan] {preview}')

if __name__ == "__main__":
    test_demo_responses()
