#!/usr/bin/env python3
"""
Test the improved token counting functionality
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ai_cli.utils import count_tokens, format_token_count, get_real_time_token_count

def test_token_counting():
    """Test the improved token counting"""
    print("🧪 Testing Improved Token Counting")
    print("=" * 50)
    
    test_texts = [
        "Hello world",
        "This is a longer sentence with multiple words and punctuation marks.",
        "Short.",
        "A very long message that contains many words, punctuation marks, and should demonstrate the improved token counting accuracy compared to the old character-divided-by-four method.",
        "Code example: def hello(): print('world')",
        "🤖 Emojis and special characters! @#$%^&*()"
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\nTest {i}: '{text}'")
        
        # Test basic token counting
        token_count = count_tokens(text)
        formatted = format_token_count(token_count)
        
        # Test real-time token counting
        real_time_info = get_real_time_token_count(text)
        
        print(f"  Tokens: {token_count}")
        print(f"  Formatted: {formatted}")
        print(f"  Words: {real_time_info['words']}")
        print(f"  Characters: {real_time_info['characters']}")
        print(f"  Real-time formatted: {real_time_info['formatted']}")
    
    print("\n✅ Token counting tests completed!")

if __name__ == "__main__":
    test_token_counting()
