"""
Enhanced Claude-like chat interface for AI CLI
"""

import sys
import time
import threading
import re
from typing import List, Optional
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.markdown import Markdown
from rich.live import Live
from rich.layout import Layout
from rich.align import Align
from rich.columns import Columns
from rich.box import ROUNDED
from prompt_toolkit import prompt
from prompt_toolkit.history import InMemoryHistory
from prompt_toolkit.auto_suggest import AutoSuggestFromHistory
from prompt_toolkit.completion import WordCompleter
from prompt_toolkit.key_binding import KeyBindings
from prompt_toolkit.formatted_text import HTML
from prompt_toolkit.styles import Style

from .config import Config
from .api_client import OpenRouterClient, Message, APIError
from .utils import (
    extract_code_blocks, 
    format_code_block, 
    count_tokens, 
    format_token_count,
    format_token_count_with_cost,
    get_real_time_token_count
)


class EnhancedChatInterface:
    """Enhanced Claude-like chat interface"""

    def __init__(self, config: Config):
        self.config = config
        self.console = Console()
        self.client = OpenRouterClient(config)
        self.conversation_history: List[Message] = []
        self.history = InMemoryHistory()
        self.demo_mode = config.is_demo_mode
        self.token_counter_active = True  # Default enabled
        self.current_input = ""
        
        # Claude-like styling
        self.style = Style.from_dict({
            'input-prompt': '#00aa00 bold',
            'token-counter': '#888888',
            'ai-response': '#0066cc bold',
            'demo-mode': '#ffaa00',
            'thinking': '#ff6600',
            'writing': '#0066cc',
            'testing': '#00aa00',
        })

        # Setup command completion
        self.completer = WordCompleter([
            '/help', '/clear', '/history', '/exit', '/quit', '/demo', '/tokens', '/cost'
        ])
        
        # Setup key bindings
        self.key_bindings = KeyBindings()
        self._setup_key_bindings()

    def _setup_key_bindings(self):
        """Setup Claude-like keyboard shortcuts"""
        @self.key_bindings.add('c-t')
        def toggle_token_counter(event):
            """Toggle token counter with Ctrl+T"""
            self.token_counter_active = not self.token_counter_active
            
        @self.key_bindings.add('c-d')
        def toggle_demo_mode(event):
            """Toggle demo mode with Ctrl+D"""
            self.demo_mode = not self.demo_mode
            
        @self.key_bindings.add('escape')
        def clear_input(event):
            """Clear input with Escape"""
            event.app.current_buffer.text = ""

    def start(self):
        """Start the enhanced chat session"""
        self._display_claude_welcome()
        
        # Test connection if not in demo mode
        if not self.demo_mode and not self._test_connection_silently():
            return
        
        # Main chat loop with enhanced UI
        while True:
            try:
                user_input = self._get_claude_input()

                if not user_input.strip():
                    continue

                # Handle commands
                if user_input.startswith('/'):
                    if self._handle_command(user_input):
                        continue
                    else:
                        break

                # Process user message with Claude-like styling
                self._process_message_claude_style(user_input)

            except KeyboardInterrupt:
                self._display_goodbye()
                break
            except Exception as e:
                self.console.print(f"[red]Error: {e}[/red]")

    def _display_claude_welcome(self):
        """Display Claude-like welcome interface"""
        # Clear screen for clean start
        self.console.clear()
        
        # Main title with Claude styling
        title = Text("Claude-like AI Assistant", style="bold cyan")
        
        # Status indicators
        status_items = []
        if self.demo_mode:
            status_items.append(Text("🎭 Demo Mode", style="yellow"))
        else:
            status_items.append(Text("🌐 Connected", style="green"))
            
        if self.token_counter_active:
            status_items.append(Text("📊 Token Counter", style="cyan"))
        
        status_text = Text(" • ").join(status_items)
        
        # Welcome content with instructions
        welcome_content = Text.assemble(
            ("Welcome to your AI assistant!\n\n", "cyan"),
            ("💬 ", ""), ("Start typing to begin a conversation\n", ""),
            ("⌨️  ", ""), ("Press ", "dim"), ("Ctrl+T", "bold"), (" to toggle token counter\n", "dim"),
            ("🎭 ", ""), ("Press ", "dim"), ("Ctrl+D", "bold"), (" to toggle demo mode\n", "dim"),
            ("❓ ", ""), ("Type ", "dim"), ("/help", "bold"), (" for more commands\n", "dim"),
            ("🚪 ", ""), ("Press ", "dim"), ("Ctrl+C", "bold"), (" to exit", "dim")
        )
        
        # Create main panel with Claude-like design
        main_panel = Panel(
            Align.center(
                Text.assemble(
                    title, "\n\n",
                    status_text, "\n\n",
                    welcome_content
                )
            ),
            box=ROUNDED,
            border_style="cyan",
            padding=(2, 4),
            title="🤖 AI Assistant",
            title_align="center"
        )
        
        self.console.print(main_panel)
        self.console.print()

    def _get_claude_input(self) -> str:
        """Get user input with Claude-like styling and real-time features"""
        try:
            # Clean interface prompt
            prompt_html = '<style fg="#00aa00" bold>You</style>: '
            
            # Get input with enhanced features
            user_input = prompt(
                HTML(prompt_html),
                history=self.history,
                auto_suggest=AutoSuggestFromHistory(),
                completer=self.completer,
                complete_style="column",
                key_bindings=self.key_bindings,
                style=self.style
            )
            
            # Clean interface - no user input token display
            
            return user_input
            
        except (EOFError, KeyboardInterrupt):
            raise KeyboardInterrupt

    def _display_input_tokens(self, text: str):
        """Removed - clean interface hides user input token details"""
        pass

    def _test_connection_silently(self) -> bool:
        """Test API connection silently"""
        try:
            success, message = self.client.test_connection()
            if not success:
                self.demo_mode = True
                self.console.print(f"[yellow]Note: AI connection unavailable, using local responses.[/yellow]")
                self.console.print()
                return False
            return True
        except Exception as e:
            self.demo_mode = True
            self.console.print(f"[yellow]Note: AI connection unavailable, using local responses.[/yellow]")
            self.console.print()
            return False

    def _display_goodbye(self):
        """Display Claude-like goodbye message"""
        goodbye_panel = Panel(
            Align.center(
                Text.assemble(
                    ("Thanks for chatting! 👋\n", "cyan bold"),
                    ("Have a wonderful day!", "cyan")
                )
            ),
            box=ROUNDED,
            border_style="cyan",
            padding=(1, 2),
            title="Goodbye"
        )
        self.console.print(goodbye_panel)

    def _process_message_claude_style(self, user_input: str):
        """Process user message with Claude-like styling and animations"""
        # Add user message to history
        user_message = Message(role="user", content=user_input)
        self.conversation_history.append(user_message)

        if self.demo_mode:
            # Demo mode with enhanced animations
            self._handle_demo_response_with_animation(user_input)
            return

        # Real API call with Claude-like animations
        messages = self.conversation_history.copy()

        try:
            ai_content, response_time = self._get_ai_response_with_claude_animation(messages)

            if ai_content:
                ai_message = Message(role="assistant", content=ai_content)
                self.conversation_history.append(ai_message)
                self._display_claude_ai_response(ai_content, response_time)
            else:
                self.console.print("[red]❌ No response received[/red]")

        except Exception as e:
            self.console.print("[yellow]⚠️  Switching to demo mode...[/yellow]")
            self.demo_mode = True
            self._handle_demo_response_with_animation(user_input)

    def _get_ai_response_with_claude_animation(self, messages: List[Message]) -> tuple[Optional[str], float]:
        """Get AI response with Claude-like 3-stage animation and timing"""
        start_time = time.time()

        try:
            # Stage 1: Thinking (with wandering dots like Claude)
            with Live(self._create_claude_thinking_animation(), console=self.console, refresh_per_second=4):
                time.sleep(1.8)
                response = self.client.chat_completion(messages)

            if 'choices' in response and len(response['choices']) > 0:
                ai_content = response['choices'][0]['message']['content']

                # Stage 2: Writing (if code detected)
                has_code = '```' in ai_content or any(keyword in ai_content.lower()
                                                   for keyword in ['def ', 'function', 'class ', 'import '])

                if has_code:
                    with Live(self._create_claude_writing_animation(), console=self.console, refresh_per_second=4):
                        time.sleep(1.2)

                # Stage 3: Testing (for code responses)
                if has_code and ('test' in ai_content.lower() or 'example' in ai_content.lower()):
                    with Live(self._create_claude_testing_animation(), console=self.console, refresh_per_second=4):
                        time.sleep(1.0)

                end_time = time.time()
                response_time = end_time - start_time
                return ai_content, response_time

            return None, 0.0

        except Exception as e:
            return None, 0.0

    def _create_claude_thinking_animation(self):
        """Create Claude-like thinking animation with wandering dots"""
        return Text.assemble(
            ("🤔 ", ""),
            ("Thinking", "bold orange1"),
            ("...", "orange1 blink")
        )

    def _create_claude_writing_animation(self):
        """Create Claude-like writing animation"""
        return Text.assemble(
            ("✍️  ", ""),
            ("Writing", "bold blue"),
            ("...", "blue blink")
        )

    def _create_claude_testing_animation(self):
        """Create Claude-like testing animation"""
        return Text.assemble(
            ("🧪 ", ""),
            ("Testing", "bold green"),
            ("...", "green blink")
        )

    def _handle_demo_response_with_animation(self, user_input: str):
        """Handle demo response with Claude-like animations and timing"""
        import random
        start_time = time.time()

        # Simulate thinking
        with Live(self._create_claude_thinking_animation(), console=self.console, refresh_per_second=4):
            time.sleep(1.5)

        # Check for code-related requests
        code_keywords = ['code', 'function', 'python', 'javascript', 'program', 'script', 'algorithm', 'write', 'create']
        has_code_request = any(keyword in user_input.lower() for keyword in code_keywords)

        if has_code_request:
            with Live(self._create_claude_writing_animation(), console=self.console, refresh_per_second=4):
                time.sleep(1.2)
            with Live(self._create_claude_testing_animation(), console=self.console, refresh_per_second=4):
                time.sleep(1.0)

        # Generate demo response
        demo_responses = self._get_demo_responses(user_input)
        response = random.choice(demo_responses)

        # Add to conversation history
        ai_message = Message(role="assistant", content=response)
        self.conversation_history.append(ai_message)

        # Calculate response time and display response
        end_time = time.time()
        response_time = end_time - start_time
        self._display_claude_ai_response(response, response_time)

    def _get_demo_responses(self, user_input: str) -> List[str]:
        """Get contextual demo responses with improved pattern matching"""
        user_lower = user_input.lower()

        # Greeting responses
        if any(keyword in user_lower for keyword in ['hello', 'hi', 'hey']):
            return [
                "Hello! I'm your AI assistant running in demo mode. How can I help you today?",
                "Hi there! Welcome to the demo mode. I'm here to assist you with any questions or tasks.",
                "Hey! Great to meet you. I'm running in demonstration mode - what would you like to explore?"
            ]

        # C programming requests (handle typos like "progarm" for "program")
        elif any(keyword in user_lower for keyword in ['c program', 'c progarm', 'c code']) and any(keyword in user_lower for keyword in ['odd', 'even']):
            if 'odd' in user_lower:
                return [
                    """Here's a C program to check if a number is odd:

```c
#include <stdio.h>

int main() {
    int number;

    printf("Enter a number: ");
    scanf("%d", &number);

    if (number % 2 != 0) {
        printf("%d is an odd number.\\n", number);
    } else {
        printf("%d is an even number.\\n", number);
    }

    return 0;
}
```

This program uses the modulo operator (%) to check if a number is odd!"""
                ]
            else:  # even
                return [
                    """Here's a C program to check if a number is even:

```c
#include <stdio.h>

int main() {
    int number;

    printf("Enter a number: ");
    scanf("%d", &number);

    if (number % 2 == 0) {
        printf("%d is an even number.\\n", number);
    } else {
        printf("%d is an odd number.\\n", number);
    }

    return 0;
}
```

This program uses the modulo operator (%) to check if a number is even!"""
                ]

        # Java programming requests - list numbers 1 to 100
        elif 'java' in user_lower and any(keyword in user_lower for keyword in ['list', 'print', '1 to 100', '1-100', 'numbers']):
            return [
                """Here's a Java program to list numbers from 1 to 100:

```java
public class NumberList {
    public static void main(String[] args) {
        System.out.println("Numbers from 1 to 100:");

        for (int i = 1; i <= 100; i++) {
            System.out.print(i + " ");

            // Print 10 numbers per line for better formatting
            if (i % 10 == 0) {
                System.out.println();
            }
        }
    }
}
```

This program uses a for loop to iterate from 1 to 100 and prints each number!"""
            ]

        # Prime number requests
        elif any(keyword in user_lower for keyword in ['prime']):
            return [
                """Here's a Java program to check if a number is prime:

```java
public class PrimeChecker {
    public static boolean isPrime(int n) {
        if (n <= 1) return false;
        if (n <= 3) return true;
        if (n % 2 == 0 || n % 3 == 0) return false;

        for (int i = 5; i * i <= n; i += 6) {
            if (n % i == 0 || n % (i + 2) == 0)
                return false;
        }
        return true;
    }

    public static void main(String[] args) {
        int number = 29;
        if (isPrime(number)) {
            System.out.println(number + " is prime");
        } else {
            System.out.println(number + " is not prime");
        }
    }
}
```

This algorithm efficiently checks primality using the 6k±1 optimization!"""
            ]

        # General programming requests - "any program"
        elif any(keyword in user_lower for keyword in ['any program', 'other program', 'another program']):
            return [
                """Here's a simple Python calculator program:

```python
def calculator():
    print("Simple Calculator")
    print("Operations: +, -, *, /")

    while True:
        try:
            num1 = float(input("Enter first number: "))
            operator = input("Enter operator (+, -, *, /): ")
            num2 = float(input("Enter second number: "))

            if operator == '+':
                result = num1 + num2
            elif operator == '-':
                result = num1 - num2
            elif operator == '*':
                result = num1 * num2
            elif operator == '/':
                if num2 != 0:
                    result = num1 / num2
                else:
                    print("Error: Division by zero!")
                    continue
            else:
                print("Invalid operator!")
                continue

            print(f"Result: {num1} {operator} {num2} = {result}")

            if input("Continue? (y/n): ").lower() != 'y':
                break

        except ValueError:
            print("Invalid input! Please enter numbers.")

calculator()
```

This is a functional calculator program with error handling!""",
                """Here's a Python program to find factorial of a number:

```python
def factorial(n):
    \"\"\"Calculate factorial using recursion\"\"\"
    if n == 0 or n == 1:
        return 1
    else:
        return n * factorial(n - 1)

def factorial_iterative(n):
    \"\"\"Calculate factorial using iteration\"\"\"
    result = 1
    for i in range(1, n + 1):
        result *= i
    return result

# Test both methods
number = 5
print(f"Factorial of {number} (recursive): {factorial(number)}")
print(f"Factorial of {number} (iterative): {factorial_iterative(number)}")
```

This shows both recursive and iterative approaches to calculate factorial!"""
            ]

        elif any(keyword in user_lower for keyword in ['code', 'python', 'function']):
            return [
                """Here's a simple Python function example:

```python
def greet(name):
    \"\"\"A friendly greeting function\"\"\"
    return f"Hello, {name}! Welcome to the demo!"

# Example usage
print(greet("User"))
```

This is a demo response showing how I can help with coding tasks!""",
                """I'd be happy to help with Python code! Here's a basic example:

```python
def calculate_area(radius):
    \"\"\"Calculate the area of a circle\"\"\"
    import math
    return math.pi * radius ** 2

# Test the function
area = calculate_area(5)
print(f"Area: {area:.2f}")
```

*Note: This is a simulated response in demo mode.*"""
            ]

        elif any(keyword in user_lower for keyword in ['what is your name', 'what is you name', 'who are you', 'what are you', 'which ai', 'you are which']):
            return [
                "I'm an AI assistant powered by Qwen 2.5 Coder, designed to help with coding, explanations, and general questions. In demo mode, I provide simulated responses to show you how the interface works!",
                "I'm your AI coding assistant running on the Qwen 2.5 Coder model. This is demo mode, so responses are simulated, but the real version provides detailed help with programming and other topics."
            ]

        elif any(keyword in user_lower for keyword in ['my name is', 'i am', 'i\'m']):
            # Extract name if possible
            name = "there"
            if 'my name is' in user_lower:
                name_part = user_input.split('my name is')[-1].strip()
                name = name_part.split()[0] if name_part else "there"
            elif 'i am' in user_lower:
                name_part = user_input.split('i am')[-1].strip()
                name = name_part.split()[0] if name_part else "there"
            elif 'i\'m' in user_lower:
                name_part = user_input.split('i\'m')[-1].strip()
                name = name_part.split()[0] if name_part else "there"

            return [
                f"Nice to meet you, {name}! I'm your AI assistant. How can I help you today?",
                f"Hello {name}! Great to meet you. What can I assist you with?"
            ]

        else:
            return [
                f"I'd be happy to help you with '{user_input}'. Could you provide more details about what you're looking for?",
                f"That's an interesting question about '{user_input}'. What specific aspect would you like me to explain or help with?",
                "I'm here to help with coding questions, explanations, and general assistance. What would you like to know more about?",
                "I can assist with programming questions, provide code examples, and explain concepts. What specific help do you need?"
            ]

    def _display_claude_ai_response(self, content: str, response_time: float = 0.0):
        """Display AI response with clean token counting and timing"""
        self.console.print()

        # Clean token display - count characters as tokens for AI responses
        char_count = len(content)

        # Create clean header
        if self.demo_mode:
            header = Text.assemble(
                ("🤖 ", ""),
                ("Claude-like AI", "bold blue"),
                (" [Demo]", "yellow")
            )
        else:
            header = Text.assemble(
                ("🤖 ", ""),
                ("Claude-like AI", "bold blue")
            )

        self.console.print(header)

        # Display AI response tokens and timing in one clean line
        if response_time > 0:
            self.console.print(f"[dim]AI Response: {char_count} tokens | {response_time:.1f}s[/dim]")
        else:
            self.console.print(f"[dim]AI Response: {char_count} tokens[/dim]")

        self.console.print()

        # Process and display content with enhanced formatting
        code_blocks = extract_code_blocks(content)

        if code_blocks:
            # Handle mixed content with code blocks
            remaining_content = content

            for code, language in code_blocks:
                code_pattern = f"```{language or ''}\n{re.escape(code)}\n```"
                remaining_content = re.sub(code_pattern, "[CODE_BLOCK_PLACEHOLDER]", remaining_content, count=1)

            # Render content with Claude-like styling
            if remaining_content.strip() and remaining_content.strip() != "[CODE_BLOCK_PLACEHOLDER]":
                parts = remaining_content.split("[CODE_BLOCK_PLACEHOLDER]")

                for i, part in enumerate(parts):
                    if part.strip():
                        # Enhanced markdown rendering
                        markdown = Markdown(part)
                        self.console.print(Panel(markdown, border_style="blue", box=ROUNDED, padding=(0, 1)))

                    # Display code block with enhanced styling
                    if i < len(code_blocks):
                        code, language = code_blocks[i]
                        formatted_code = format_code_block(code, language)
                        self.console.print(formatted_code)
            else:
                # Only code blocks
                for code, language in code_blocks:
                    formatted_code = format_code_block(code, language)
                    self.console.print(formatted_code)
        else:
            # Regular text response with Claude-like panel
            markdown = Markdown(content)
            response_panel = Panel(
                markdown,
                border_style="blue",
                box=ROUNDED,
                padding=(1, 2)
            )
            self.console.print(response_panel)

        self.console.print()

    def _handle_command(self, command: str) -> bool:
        """Handle chat commands with Claude-like responses"""
        command = command.lower().strip()

        if command in ['/exit', '/quit']:
            return False

        elif command == '/help':
            self._show_claude_help()

        elif command == '/clear':
            self.conversation_history.clear()
            self.console.clear()
            self._display_claude_welcome()

        elif command == '/history':
            self._show_claude_history()

        elif command == '/demo':
            self.demo_mode = not self.demo_mode
            status = "enabled" if self.demo_mode else "disabled"
            self.console.print(f"[yellow]🎭 Demo mode {status}[/yellow]")

        elif command == '/tokens':
            self.token_counter_active = not self.token_counter_active
            status = "enabled" if self.token_counter_active else "disabled"
            self.console.print(f"[cyan]📊 Token counter {status}[/cyan]")

        elif command == '/cost':
            self._show_session_cost_summary()

        else:
            self.console.print(f"[yellow]❓ Unknown command: {command}[/yellow]")
            self.console.print("[cyan]💡 Type '/help' for available commands[/cyan]")

        return True

    def _show_claude_help(self):
        """Show help with Claude-like styling"""
        help_content = Text.assemble(
            ("Commands:\n", "bold cyan"),
            ("• ", ""), ("/help", "bold"), ("     - Show this help message\n", ""),
            ("• ", ""), ("/clear", "bold"), ("    - Clear conversation and restart\n", ""),
            ("• ", ""), ("/history", "bold"), ("  - Show conversation history\n", ""),
            ("• ", ""), ("/demo", "bold"), ("     - Toggle demo mode\n", ""),
            ("• ", ""), ("/tokens", "bold"), ("   - Toggle token counter\n", ""),
            ("• ", ""), ("/cost", "bold"), ("     - Show session cost summary\n", ""),
            ("• ", ""), ("/exit", "bold"), ("     - Exit the application\n\n", ""),

            ("Keyboard Shortcuts:\n", "bold cyan"),
            ("• ", ""), ("Ctrl+T", "bold"), ("   - Toggle token counter\n", ""),
            ("• ", ""), ("Ctrl+D", "bold"), ("   - Toggle demo mode\n", ""),
            ("• ", ""), ("Ctrl+C", "bold"), ("   - Exit application\n", ""),
            ("• ", ""), ("Escape", "bold"), ("   - Clear current input\n\n", ""),

            ("Features:\n", "bold cyan"),
            ("• Real-time token counting with cost estimation\n", ""),
            ("• 3-stage AI response animations (thinking → writing → testing)\n", ""),
            ("• Demo mode with simulated responses\n", ""),
            ("• Claude-like interface design and interactions\n", ""),
            ("• Enhanced code block formatting and syntax highlighting", "")
        )

        help_panel = Panel(
            help_content,
            title="🆘 Help & Features",
            border_style="cyan",
            box=ROUNDED,
            padding=(1, 2)
        )
        self.console.print(help_panel)

    def _show_claude_history(self):
        """Show conversation history with Claude-like styling"""
        if not self.conversation_history:
            no_history_panel = Panel(
                Text("No conversation history yet.\nStart chatting to see your messages here!", style="yellow"),
                title="📜 Conversation History",
                border_style="yellow",
                box=ROUNDED,
                padding=(1, 2)
            )
            self.console.print(no_history_panel)
            return

        history_content = Text()
        for i, message in enumerate(self.conversation_history, 1):
            role_color = "green" if message.role == "user" else "blue"
            role_icon = "👤" if message.role == "user" else "🤖"
            role_name = "You" if message.role == "user" else "AI"

            history_content.append(f"{role_icon} ", style="")
            history_content.append(f"{i}. {role_name}: ", style=f"bold {role_color}")

            # Truncate long messages
            content_preview = message.content[:80] + "..." if len(message.content) > 80 else message.content
            history_content.append(f"{content_preview}\n\n", style="")

        history_panel = Panel(
            history_content,
            title="📜 Conversation History",
            border_style="cyan",
            box=ROUNDED,
            padding=(1, 2)
        )
        self.console.print(history_panel)

    def _show_session_cost_summary(self):
        """Show session cost summary with enhanced pricing information"""
        from .utils import get_session_cost_summary

        if not self.conversation_history:
            no_cost_panel = Panel(
                Text("No conversation yet.\nStart chatting to see token usage and costs!", style="yellow"),
                title="💰 Session Cost Summary",
                border_style="yellow",
                box=ROUNDED,
                padding=(1, 2)
            )
            self.console.print(no_cost_panel)
            return

        # Get cost summary
        cost_summary = get_session_cost_summary(self.conversation_history, self.config.model, self.demo_mode)

        # Create detailed cost display
        cost_content = Text()

        # Header
        if self.demo_mode:
            cost_content.append("💰 Session Cost Summary (Demo Mode)\n\n", style="bold yellow")
        else:
            cost_content.append("💰 Session Cost Summary\n\n", style="bold green")

        # Token breakdown
        cost_content.append("📊 Token Usage:\n", style="bold cyan")
        cost_content.append(f"   • Total tokens: {cost_summary['total_tokens']:,}\n", style="")
        cost_content.append(f"   • Your messages: {cost_summary['user_tokens']:,} tokens\n", style="green")
        cost_content.append(f"   • AI responses: {cost_summary['ai_tokens']:,} tokens\n", style="blue")
        cost_content.append(f"   • Messages: {len(self.conversation_history)} total\n\n", style="")

        # Cost breakdown
        cost_content.append("💳 Cost Breakdown:\n", style="bold cyan")
        cost_content.append(f"   • Model: {cost_summary['model']}\n", style="")

        if self.demo_mode:
            cost_content.append(f"   • Estimated cost: {cost_summary['formatted_cost']} (Demo)\n", style="yellow")
            cost_content.append("   • Note: Demo mode shows simulated pricing\n", style="dim yellow")
        else:
            if cost_summary['total_cost'] == 0:
                cost_content.append("   • Total cost: Free! 🎉\n", style="green")
            else:
                cost_content.append(f"   • Total cost: {cost_summary['formatted_cost']}\n", style="green")

        cost_content.append("\n📈 Efficiency Tips:\n", style="bold cyan")
        if cost_summary['total_tokens'] > 10000:
            cost_content.append("   • Consider using /clear to start fresh\n", style="yellow")
        cost_content.append("   • Use /tokens to monitor real-time usage\n", style="")
        cost_content.append("   • Shorter messages = lower costs\n", style="")

        cost_panel = Panel(
            cost_content,
            title="💰 Session Cost Summary",
            border_style="green" if not self.demo_mode else "yellow",
            box=ROUNDED,
            padding=(1, 2)
        )
        self.console.print(cost_panel)
