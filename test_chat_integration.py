#!/usr/bin/env python3
"""
Test the integrated chat interface with clean token counter
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from rich.console import Console
from ai_cli.config import Config
from ai_cli.chat import ChatInterface

def test_chat_interface():
    """Test the chat interface initialization and basic functionality"""
    console = Console()
    console.print("[bold cyan]🧪 Testing Chat Interface Integration[/bold cyan]")
    console.print()
    
    try:
        # Initialize config and chat interface
        config = Config()
        chat = ChatInterface(config)
        
        console.print("[green]✅ Chat interface initialized successfully[/green]")
        
        # Test the display methods directly
        console.print("\n[bold]Testing AI response display:[/bold]")
        
        # Test AI response display
        test_content = "Hello! This is a test response from the AI assistant."
        test_time = 2.5
        
        chat._display_ai_response(test_content, test_time)
        
        console.print("[green]✅ AI response display working correctly[/green]")
        
        # Test welcome display
        console.print("\n[bold]Testing welcome message:[/bold]")
        chat._display_welcome()
        
        console.print("[green]✅ Welcome message display working correctly[/green]")
        
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Error testing chat interface: {e}[/red]")
        return False

def test_enhanced_chat_interface():
    """Test the enhanced chat interface"""
    console = Console()
    console.print("[bold cyan]🧪 Testing Enhanced Chat Interface[/bold cyan]")
    console.print()
    
    try:
        from ai_cli.enhanced_chat import EnhancedChatInterface
        
        # Initialize config and enhanced chat interface
        config = Config()
        enhanced_chat = EnhancedChatInterface(config)
        
        console.print("[green]✅ Enhanced chat interface initialized successfully[/green]")
        
        # Test the display methods directly
        console.print("\n[bold]Testing enhanced AI response display:[/bold]")
        
        # Test AI response display with timing
        test_content = "This is a test response from the enhanced Claude-like AI interface."
        test_time = 3.2
        
        enhanced_chat._display_claude_ai_response(test_content, test_time)
        
        console.print("[green]✅ Enhanced AI response display working correctly[/green]")
        
        return True
        
    except Exception as e:
        console.print(f"[red]❌ Error testing enhanced chat interface: {e}[/red]")
        return False

if __name__ == "__main__":
    console = Console()
    
    # Test both interfaces
    basic_success = test_chat_interface()
    print()
    enhanced_success = test_enhanced_chat_interface()
    
    print("\n" + "="*60)
    if basic_success and enhanced_success:
        console.print("[bold green]🎉 All integration tests passed![/bold green]")
        console.print("\n[yellow]Ready to use the clean chat interface:[/yellow]")
        console.print("• Run: python -m ai_cli.main")
        console.print("• Or: python -m ai_cli.main --enhanced")
        console.print("\n[dim]Features implemented:[/dim]")
        console.print("• Clean user input (no token breakdown)")
        console.print("• AI response tokens = character count")
        console.print("• Response timing display")
        console.print("• Minimal, focused interface")
    else:
        console.print("[bold red]❌ Some tests failed. Please check the implementation.[/bold red]")
