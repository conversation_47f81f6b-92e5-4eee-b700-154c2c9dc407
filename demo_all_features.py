#!/usr/bin/env python3
"""
Comprehensive demonstration of all enhanced chat features
"""

import sys
import os
import time

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ai_cli.config import Config
from ai_cli.enhanced_chat import EnhancedChatInterface
from ai_cli.utils import get_real_time_token_count, get_session_cost_summary
from ai_cli.api_client import Message
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.live import Live
from rich.columns import Columns

def demo_header():
    """Display demo header"""
    console = Console()
    console.clear()
    
    title = Text("🎉 Enhanced Chat Interface Demo", style="bold cyan")
    subtitle = Text("Showcasing Claude-like UI with Advanced Features", style="cyan")
    
    header_panel = Panel(
        Text.assemble(title, "\n", subtitle),
        title="Demo Showcase",
        border_style="cyan",
        padding=(2, 4)
    )
    
    console.print(header_panel)
    console.print()

def demo_token_counter():
    """Demonstrate token counter features"""
    console = Console()
    
    console.print("[bold blue]📊 Token Counter Demonstration[/bold blue]")
    console.print()
    
    examples = [
        ("Simple greeting", "Hello! How are you today?"),
        ("Code request", "Write a Python function to calculate the factorial of a number"),
        ("Complex code", """```python
def quicksort(arr):
    if len(arr) <= 1:
        return arr
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    return quicksort(left) + middle + quicksort(right)
```""")
    ]
    
    for title, text in examples:
        console.print(f"[cyan]Example: {title}[/cyan]")
        token_info = get_real_time_token_count(text, "qwen/qwen-2.5-coder-32b-instruct:free", demo_mode=True)
        
        console.print(f"   Text: {text[:60]}{'...' if len(text) > 60 else ''}")
        console.print(f"   📊 {token_info['formatted']} | {token_info['words']} words | {token_info['characters']} chars")
        console.print()

def demo_animations():
    """Demonstrate animation system"""
    console = Console()
    
    console.print("[bold green]🎬 Animation System Demonstration[/bold green]")
    console.print()
    
    config = Config()
    config.demo_mode = True
    chat = EnhancedChatInterface(config)
    
    # Demo thinking animation
    console.print("[cyan]Stage 1: AI Thinking Animation[/cyan]")
    with Live(chat._create_claude_thinking_animation(), console=console, refresh_per_second=4):
        time.sleep(2)
    
    # Demo writing animation
    console.print("[cyan]Stage 2: AI Writing Code Animation[/cyan]")
    with Live(chat._create_claude_writing_animation(), console=console, refresh_per_second=4):
        time.sleep(2)
    
    # Demo testing animation
    console.print("[cyan]Stage 3: AI Testing Animation[/cyan]")
    with Live(chat._create_claude_testing_animation(), console=console, refresh_per_second=4):
        time.sleep(2)
    
    console.print("[green]✅ Animation sequence complete![/green]")
    console.print()

def demo_cost_summary():
    """Demonstrate cost summary features"""
    console = Console()
    
    console.print("[bold yellow]💰 Cost Summary Demonstration[/bold yellow]")
    console.print()
    
    # Create realistic conversation history
    mock_conversation = [
        Message(role="user", content="Hello! Can you help me learn Python?"),
        Message(role="assistant", content="Absolutely! I'd be happy to help you learn Python. What specific aspect would you like to start with?"),
        Message(role="user", content="Can you show me how to create a simple calculator?"),
        Message(role="assistant", content="""Here's a simple calculator in Python:

```python
def calculator():
    print("Simple Calculator")
    print("Operations: +, -, *, /")
    
    while True:
        try:
            num1 = float(input("Enter first number: "))
            operation = input("Enter operation (+, -, *, /): ")
            num2 = float(input("Enter second number: "))
            
            if operation == '+':
                result = num1 + num2
            elif operation == '-':
                result = num1 - num2
            elif operation == '*':
                result = num1 * num2
            elif operation == '/':
                if num2 != 0:
                    result = num1 / num2
                else:
                    print("Error: Division by zero!")
                    continue
            else:
                print("Invalid operation!")
                continue
            
            print(f"Result: {result}")
            
            if input("Continue? (y/n): ").lower() != 'y':
                break
                
        except ValueError:
            print("Invalid input! Please enter numbers.")

# Run the calculator
calculator()
```

This calculator handles basic arithmetic operations with error checking for division by zero and invalid inputs."""),
        Message(role="user", content="That's great! Can you explain how the error handling works?"),
        Message(role="assistant", content="Certainly! The error handling in this calculator uses several techniques:\n\n1. **try-except blocks**: Catches ValueError when users enter non-numeric input\n2. **Division by zero check**: Prevents crashes when dividing by zero\n3. **Input validation**: Checks for valid operations\n4. **User-friendly messages**: Provides clear feedback for errors\n\nThis makes the calculator robust and user-friendly!")
    ]
    
    cost_summary = get_session_cost_summary(mock_conversation, "qwen/qwen-2.5-coder-32b-instruct:free", demo_mode=True)
    
    # Display cost breakdown
    cost_panel = Panel(
        Text.assemble(
            (f"💬 Messages: {len(mock_conversation)}\n", ""),
            (f"📊 Total Tokens: {cost_summary['total_tokens']:,}\n", "cyan"),
            (f"👤 User Tokens: {cost_summary['user_tokens']:,}\n", "green"),
            (f"🤖 AI Tokens: {cost_summary['ai_tokens']:,}\n", "blue"),
            (f"💳 Session Cost: {cost_summary['formatted_cost']}\n", "yellow"),
            (f"🏷️  Model: {cost_summary['model']}", "dim")
        ),
        title="💰 Session Cost Summary",
        border_style="yellow",
        padding=(1, 2)
    )
    
    console.print(cost_panel)
    console.print()

def demo_ui_features():
    """Demonstrate UI features"""
    console = Console()
    
    console.print("[bold magenta]🎨 UI Features Demonstration[/bold magenta]")
    console.print()
    
    # Demo panels and styling
    features = [
        ("🎭 Demo Mode", "Automatic fallback with mock responses", "yellow"),
        ("📊 Token Counter", "Real-time usage tracking", "cyan"),
        ("🎬 Animations", "3-stage response processing", "green"),
        ("⌨️  Shortcuts", "Ctrl+T, Ctrl+D, Ctrl+C", "blue"),
        ("💬 Commands", "/help, /cost, /demo, /tokens", "magenta"),
        ("🎨 Styling", "Claude-like interface design", "red")
    ]
    
    panels = []
    for title, description, color in features:
        panel = Panel(
            Text.assemble(
                (title, f"bold {color}"),
                ("\n", ""),
                (description, color)
            ),
            border_style=color,
            padding=(1, 1),
            width=25
        )
        panels.append(panel)
    
    # Display in columns
    columns = Columns(panels, equal=True, expand=True)
    console.print(columns)
    console.print()

def demo_commands():
    """Demonstrate available commands"""
    console = Console()
    
    console.print("[bold red]⌨️  Command Demonstration[/bold red]")
    console.print()
    
    commands = [
        ("/help", "Show comprehensive help", "📚"),
        ("/clear", "Clear conversation history", "🧹"),
        ("/history", "Show conversation history", "📜"),
        ("/demo", "Toggle demo mode", "🎭"),
        ("/tokens", "Toggle token counter", "📊"),
        ("/cost", "Show session cost summary", "💰"),
        ("/exit", "Exit application", "🚪")
    ]
    
    command_text = Text()
    for cmd, desc, icon in commands:
        command_text.append(f"{icon} ", style="")
        command_text.append(f"{cmd:<10}", style="bold cyan")
        command_text.append(f" - {desc}\n", style="")
    
    command_panel = Panel(
        command_text,
        title="Available Commands",
        border_style="red",
        padding=(1, 2)
    )
    
    console.print(command_panel)
    console.print()

def main():
    """Run the complete demonstration"""
    demo_header()
    
    console = Console()
    
    # Run all demonstrations
    demo_token_counter()
    demo_animations()
    demo_cost_summary()
    demo_ui_features()
    demo_commands()
    
    # Final summary
    summary_panel = Panel(
        Text.assemble(
            ("🎉 Demonstration Complete!\n\n", "bold green"),
            ("All enhanced features have been showcased:\n", ""),
            ("✅ Real-time token counting with cost estimation\n", "green"),
            ("✅ 3-stage AI response animation system\n", "green"),
            ("✅ Claude-like UI design and styling\n", "green"),
            ("✅ Demo mode with mock pricing\n", "green"),
            ("✅ Enhanced keyboard shortcuts and commands\n", "green"),
            ("✅ Session cost tracking and summaries\n", "green"),
            ("\n🚀 Ready to use the enhanced chat interface!\n", "cyan bold"),
            ("Run: python enhanced_chat_demo.py", "cyan")
        ),
        title="🏆 Demo Summary",
        border_style="green",
        padding=(2, 3)
    )
    
    console.print(summary_panel)

if __name__ == "__main__":
    main()
