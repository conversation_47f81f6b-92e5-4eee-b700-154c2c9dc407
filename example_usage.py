#!/usr/bin/env python3
"""
Example usage of AI CLI programmatically
"""

from ai_cli.config import Config
from ai_cli.api_client import OpenRouterClient, Message, APIError

def example_api_usage():
    """Example of using the API client directly"""
    print("🤖 AI CLI API Example")
    print("=" * 40)

    # Initialize configuration
    config = Config()
    client = OpenRouterClient(config)

    # Test connection
    print("Testing API connection...")
    if client.test_connection():
        print("✓ API connection successful!")
    else:
        print("✗ API connection failed. This might be due to:")
        print("  - Network connectivity issues")
        print("  - API key limitations")
        print("  - OpenRouter service availability")
        print("\nThe CLI application is still functional for offline testing.")
        return

    # Example conversation
    messages = [
        Message(role="user", content="Hello! Can you write a simple Python function to add two numbers?")
    ]

    try:
        print("\nSending request to AI...")
        response = client.chat_completion(messages)

        if 'choices' in response and len(response['choices']) > 0:
            ai_response = response['choices'][0]['message']['content']
            print("\n🤖 AI Response:")
            print("-" * 40)
            print(ai_response)
        else:
            print("No response received from AI")

    except APIError as e:
        print(f"API Error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")

def show_cli_commands():
    """Show available CLI commands"""
    print("\n📋 Available CLI Commands:")
    print("=" * 40)
    print("ai-cli --help              # Show help")
    print("ai-cli info                # Show application info")
    print("ai-cli chat                # Start interactive chat")
    print("ai-cli chat --model MODEL  # Use specific model")
    print("ai-cli chat --api-key KEY  # Use custom API key")

    print("\n💡 Interactive Commands (within chat):")
    print("/help     # Show help")
    print("/clear    # Clear history")
    print("/history  # Show conversation history")
    print("/model    # Show current model")
    print("/exit     # Exit chat")

def test_chat_startup():
    """Test the chat startup process"""
    print("\n🧪 Testing Chat Startup Process:")
    print("=" * 40)

    try:
        from ai_cli.config import Config
        from ai_cli.chat import ChatInterface

        # Test configuration
        config = Config()
        print(f"✓ Configuration loaded (API key: {bool(config.api_key)})")

        # Test chat interface creation
        chat_interface = ChatInterface(config)
        print("✓ Chat interface created successfully")

        # Test connection (this will show the improved error message)
        print("\n🔗 Testing API connection...")
        success, message = chat_interface.client.test_connection()

        if success:
            print("✅ API connection successful!")
        else:
            print(f"⚠️  API connection failed: {message}")
            print("   This is expected if there are network issues or API limitations.")
            print("   The application will work in offline mode for testing.")

        print("\n✅ Chat startup test completed successfully!")

    except Exception as e:
        print(f"❌ Chat startup test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    example_api_usage()
    show_cli_commands()
    test_chat_startup()

    print("\n🚀 To start the interactive chat, run:")
    print("ai-cli chat")