#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run the chat interface with proper terminal handling
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ai_cli.config import Config
from ai_cli.chat import ChatInterface
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

def run_chat():
    """Run the chat interface"""
    console = Console()
    
    # Display welcome message
    welcome_text = Text("🤖 AI CLI Assistant", style="bold cyan")
    welcome_panel = Panel(
        welcome_text,
        title="Welcome",
        border_style="cyan",
        padding=(1, 2)
    )
    console.print(welcome_panel)
    console.print()
    
    # Initialize configuration
    config = Config()
    
    if not config.api_key:
        console.print("[red]Error: No API key provided. Use --api-key or set OPENROUTER_API_KEY environment variable.[/red]")
        sys.exit(1)
    
    # Start chat interface
    chat_interface = ChatInterface(config)
    
    try:
        chat_interface.start()
    except KeyboardInterrupt:
        console.print("\n[yellow]Goodbye![/yellow]")
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")

if __name__ == "__main__":
    run_chat()
