#!/usr/bin/env python3
"""
Main CLI application entry point
"""

import click
import sys
import os
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

from .chat import ChatInterface
from .enhanced_chat import EnhancedChatInterface
from .config import Config

console = Console()

@click.group(invoke_without_command=True)
@click.pass_context
@click.option('--demo', is_flag=True, help='Run in demo mode')
@click.option('--enhanced', is_flag=True, help='Use enhanced Claude-like interface')
@click.version_option(version="1.0.0")
def cli(ctx, demo, enhanced):
    """AI Assistant - Your intelligent command-line companion"""
    if ctx.invoked_subcommand is None:
        # If no subcommand is provided, start chat directly
        start_chat(demo=demo, enhanced=enhanced)

def start_chat(demo=False, enhanced=False):
    """Start the chat interface directly"""
    # Initialize configuration with defaults
    config = Config()

    # Set demo mode if requested
    if demo:
        config.demo_mode = True

    # Choose interface type
    if enhanced:
        # Use enhanced Claude-like interface
        chat_interface = EnhancedChatInterface(config)
    else:
        # Use standard interface
        chat_interface = ChatInterface(config)

    # Start chat interface
    chat_interface.start()

@cli.command()
@click.option('--demo', is_flag=True, help='Run in demo mode')
@click.option('--enhanced', is_flag=True, help='Use enhanced Claude-like interface')
def chat(demo, enhanced):
    """Start an interactive chat session with your AI assistant"""
    start_chat(demo=demo, enhanced=enhanced)

@cli.command()
def info():
    """Display information about the AI Assistant application"""
    info_text = """
    AI Assistant - Your Intelligent Command Line Companion

    Features:
    • Natural conversational AI chat
    • Code syntax highlighting
    • Token usage tracking
    • Multi-line input support
    • Command history
    • Ready to use - no setup required!

    Usage:
    Simply run: ai-cli

    That's it! No configuration needed.
    """

    console.print(Panel(info_text, title="AI Assistant Information", border_style="blue"))

def main():
    """Main entry point for the CLI application"""
    try:
        cli()
    except KeyboardInterrupt:
        console.print("\n[yellow]Goodbye![/yellow]")
        sys.exit(0)
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        sys.exit(1)

if __name__ == "__main__":
    main()