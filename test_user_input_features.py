#!/usr/bin/env python3
"""
Test the enhanced user input features including token counting and word breakdown
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ai_cli.config import Config
from ai_cli.utils import get_real_time_token_count

def test_user_input_features():
    """Test the enhanced user input features"""
    print("🧪 Testing Enhanced User Input Features")
    print("=" * 50)
    
    try:
        # Create configuration
        config = Config()
        print(f"✓ Configuration created (model: {config.model})")
        
        # Test user input processing with enhanced features
        print("\n💬 Testing enhanced user input processing...")
        
        test_messages = [
            "Hello world",
            "This is a longer message with multiple words to test the enhanced token counting and word breakdown functionality",
            "Short.",
            "Write a Python function that calculates the factorial of a number"
        ]
        
        for i, test_message in enumerate(test_messages, 1):
            print(f"\n--- Enhanced Test {i} ---")
            print(f"Input: '{test_message}'")
            
            # Simulate the enhanced user input processing
            user_input = test_message
            if user_input.strip():
                token_info = get_real_time_token_count(user_input, config.model)
                print(f"📊 Your message: {token_info['formatted']} | {token_info['words']} words | {token_info['characters']} characters")
                
                # Show detailed word breakdown
                if token_info['word_breakdown']['breakdown_text']:
                    print(f"📝 Word breakdown: {token_info['word_breakdown']['breakdown_text']}")
        
        print("\n✅ Enhanced user input features test completed!")
        
    except Exception as e:
        print(f"❌ Enhanced features test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_user_input_features()
