#!/usr/bin/env python3
"""
Quick test of the improved demo responses
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from rich.console import Console
from ai_cli.config import Config
from ai_cli.chat import ChatInterface

def quick_test():
    """Quick test of demo responses"""
    console = Console()
    console.print("[bold cyan]🧪 Quick Demo Response Test[/bold cyan]")
    console.print()
    
    # Test the offline response method directly
    config = Config()
    config.demo_mode = True
    chat = ChatInterface(config)
    
    test_inputs = [
        "make a java program to check prime no",
        "what is your name",
        "you are which ai", 
        "my name is rahul"
    ]
    
    for i, test_input in enumerate(test_inputs, 1):
        console.print(f"[bold]Test {i}:[/bold] '{test_input}'")
        chat._handle_offline_response(test_input, 2.0)
        console.print()
    
    console.print("[bold green]✅ Quick test complete![/bold green]")
    console.print("\n[yellow]The improved responses should now work properly in the actual chat interface.[/yellow]")

if __name__ == "__main__":
    quick_test()
