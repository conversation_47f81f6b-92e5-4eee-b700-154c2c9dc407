#!/usr/bin/env python3
"""
Enhanced Claude-like Chat Interface Demo
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ai_cli.config import Config
from ai_cli.enhanced_chat import EnhancedChatInterface
from rich.console import Console

def main():
    """Run the enhanced chat interface"""
    console = Console()
    
    try:
        # Initialize configuration
        config = Config()
        
        # Create and start enhanced chat interface
        chat_interface = EnhancedChatInterface(config)
        chat_interface.start()
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Goodbye! 👋[/yellow]")
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        console.print("[yellow]Please check your configuration and try again.[/yellow]")

if __name__ == "__main__":
    main()
