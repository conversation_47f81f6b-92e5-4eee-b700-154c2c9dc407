"""
OpenRouter API client for AI CLI
"""

import json
import requests
from typing import Dict, List, Optional, Generator, Tuple
from dataclasses import dataclass

from .config import Config


@dataclass
class Message:
    """Represents a chat message"""
    role: str  # 'user', 'assistant', or 'system'
    content: str


class APIError(Exception):
    """Custom exception for API errors"""
    pass


class OpenRouterClient:
    """Client for interacting with OpenRouter API"""

    def __init__(self, config: Config):
        self.config = config
        self.session = requests.Session()
        self.session.headers.update(config.headers)

    def chat_completion(self, messages: List[Message], stream: bool = False) -> Dict:
        """
        Send a chat completion request to OpenRouter API

        Args:
            messages: List of Message objects
            stream: Whether to stream the response

        Returns:
            API response as dictionary

        Raises:
            APIError: If the API request fails
        """
        url = f"{self.config.base_url}/chat/completions"

        payload = {
            "model": self.config.model,
            "messages": [{"role": msg.role, "content": msg.content} for msg in messages],
            "max_tokens": self.config.max_tokens,
            "temperature": self.config.temperature,
            "stream": stream
        }

        try:
            response = self.session.post(url, json=payload, timeout=30)
            response.raise_for_status()

            if stream:
                return self._handle_stream_response(response)
            else:
                return response.json()

        except requests.exceptions.RequestException as e:
            raise APIError(f"API request failed: {e}")
        except json.JSONDecodeError as e:
            raise APIError(f"Failed to parse API response: {e}")

    def _handle_stream_response(self, response) -> Generator[Dict, None, None]:
        """Handle streaming response from API"""
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data = line[6:]  # Remove 'data: ' prefix
                    if data.strip() == '[DONE]':
                        break
                    try:
                        yield json.loads(data)
                    except json.JSONDecodeError:
                        continue

    def get_available_models(self) -> List[Dict]:
        """
        Get list of available models from OpenRouter

        Returns:
            List of model information dictionaries
        """
        url = f"{self.config.base_url}/models"

        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            return response.json().get('data', [])
        except requests.exceptions.RequestException as e:
            raise APIError(f"Failed to fetch models: {e}")

    def test_connection(self) -> Tuple[bool, str]:
        """
        Test the API connection

        Returns:
            Tuple of (success: bool, message: str)
        """
        try:
            # Simple test with minimal request
            test_messages = [Message(role="user", content="Hi")]
            response = self.chat_completion(test_messages)

            if 'choices' in response and len(response['choices']) > 0:
                return True, "Connection successful"
            else:
                return False, "Invalid response format from API"

        except requests.exceptions.ConnectionError:
            return False, "Network connection failed - check internet connectivity"
        except requests.exceptions.Timeout:
            return False, "Request timeout - API may be slow or unavailable"
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 401:
                return False, "Authentication failed - check API key"
            elif e.response.status_code == 429:
                return False, "Rate limit exceeded - try again later"
            else:
                return False, f"HTTP error {e.response.status_code}: {e.response.text}"
        except APIError as e:
            return False, f"API error: {str(e)}"
        except Exception as e:
            return False, f"Unexpected error: {str(e)}"